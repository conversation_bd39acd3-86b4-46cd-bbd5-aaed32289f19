/* EMERGENCY DROPDOWN FIX - OVERRIDE ALL EXISTING STYLES */

/* TEST: Add red background to all nav items to check if CSS is loading */
.nav-item {
    background: red !important;
    border: 2px solid yellow !important;
}

/* Force dropdown to be visible and working */
.nav-item.dropdown {
    position: relative !important;
}

.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 250px !important;
    background: white !important;
    border: 2px solid #0B4C7A !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
    list-style: none !important;
    padding: 10px 0 !important;
    margin: 5px 0 0 0 !important;
    z-index: 999999 !important;
    display: block !important;

    /* Make it always visible for now */
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

.dropdown-menu li {
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.dropdown-menu a {
    display: block !important;
    padding: 12px 20px !important;
    color: #333 !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-left: 3px solid transparent !important;
    transition: all 0.2s ease !important;
}

.dropdown-menu a:hover {
    background-color: #f0f8ff !important;
    color: #0B4C7A !important;
    border-left-color: #2E7D32 !important;
    padding-left: 25px !important;
}

/* Force dropdown to show on hover */
.nav-item.dropdown:hover .dropdown-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    display: block !important;
}

/* Keep dropdown visible when hovering over dropdown itself */
.dropdown-menu:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    display: block !important;
}

/* Right align for last items */
.nav-item.dropdown:last-child .dropdown-menu,
.nav-item.dropdown:nth-last-child(2) .dropdown-menu {
    left: auto !important;
    right: 0 !important;
}
